import { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import { unstable_batchedUpdates, flushSync } from 'react-dom';

import { STORAGE_KEYS } from '../constants';
import { cancelFileLoading, openFolderDialog, requestFileContent, setupElectronHandlers } from '../handlers/electron-handlers';
import { applyFiltersAndSort, refreshFileTree } from '../handlers/filter-handlers';
import { electronHandlerSingleton } from '../handlers/electron-handler-singleton';
import { FileData, FileTreeMode, WorkspaceState, SystemPrompt, RolePrompt, Instruction, SelectedFileWithLines, SelectedFileReference } from '../types/file-types';
import { getSelectedFilesContent, getSelectedFilesContentWithoutInstructions } from '../utils/content-formatter';
import { resetFolderState } from '../utils/file-utils';
import { calculateFileTreeTokens, estimateTokenCount, getFileTreeModeTokens } from '../utils/token-utils';
import { enhancedFileContentCache as fileContentCache } from '../utils/enhanced-file-cache';
import { mapFileTreeSortToContentSort } from '../utils/sort-utils';
import { tokenCountCache } from '../utils/token-cache';
import { buildFolderIndex } from '../utils/folder-selection-index';

import useDocState from './use-doc-state';
import useFileSelectionState from './use-file-selection-state';
import usePersistentState from './use-persistent-state';
import useModalState from './use-modal-state';
import usePromptState from './use-prompt-state';
import { useWorkspaceState } from './use-workspace-state';
import { useTokenCounter } from './use-token-counter';

type PendingWorkspaceData = Omit<WorkspaceState, 'selectedFolder'>;

/**
 * Central application state hook implementing the single-source-of-truth pattern.
 * 
 * Architecture Overview:
 * - `allFiles`: The authoritative source for all file data in the workspace
 * - `selectedFiles`: Contains only references (paths + line ranges) to selected files
 * - Components look up file data by combining references with the source data
 * 
 * This design solves the file content flicker issue by ensuring:
 * 1. File data is never duplicated across different state variables
 * 2. All file updates flow through a single update path (updateFileWithContent)
 * 3. Selection changes don't trigger unnecessary file data updates
 * 4. Caches are properly invalidated when file content changes
 * 
 * Key patterns:
 * - Use `SelectedFileReference` for tracking selections
 * - Use `FileData` from allFiles for all file information
 * - Clear all caches when switching workspaces to prevent memory leaks
 */
const useAppState = () => {
  const isElectron = window.electron !== undefined;

  // Core state
  const [selectedFolder, setSelectedFolder] = useState(null as string | null);
  const [sortOrder, setSortOrder] = usePersistentState<string>(
    STORAGE_KEYS.SORT_ORDER,
    "tokens-desc"
  );
  const [searchTerm, setSearchTerm] = usePersistentState<string>(
    STORAGE_KEYS.SEARCH_TERM,
    ""
  );
  const [fileTreeMode, setFileTreeMode] = usePersistentState<FileTreeMode>(
    STORAGE_KEYS.FILE_TREE_MODE,
    "none"
  );
  const [exclusionPatterns, setExclusionPatterns] = usePersistentState<string[]>(
    "pasteflow-exclusion-patterns",
    [
      "**/node_modules/",
      "**/.npm/",
      "**/__pycache__/",
      "**/.pytest_cache/",
      "**/.mypy_cache/",
      "**/.gradle/",
      "**/.nuget/",
      "**/.cargo/",
      "**/.stack-work/",
      "**/.ccache/",
      "**/.idea/",
      "**/.vscode/",
      "**/*.swp",
      "**/*~",
      "**/*.tmp",
      "**/*.temp",
      "**/*.bak",
      "**/*.meta",
      "**/package-lock.json",
    ]
  );

  // Non-persistent state
  const [allFiles, setAllFiles] = useState([] as FileData[]);
  const [displayedFiles, setDisplayedFiles] = useState([] as FileData[]);
  const [expandedNodes, setExpandedNodes] = usePersistentState<Record<string, boolean>>(
    STORAGE_KEYS.EXPANDED_NODES, 
    {}
  );
  const [appInitialized, setAppInitialized] = useState(false);

  type ProcessingStatusType = {
    status: "idle" | "processing" | "complete" | "error";
    message: string;
    processed?: number;
    directories?: number;
    total?: number;
  };
  const [processingStatus, setProcessingStatus] = useState({
    status: "idle" as const,
    message: "",
    processed: 0,
    directories: 0,
    total: 0
  } as ProcessingStatusType);
  const [isLoadingCancellable, setIsLoadingCancellable] = useState(false);
  const [pendingWorkspaceData, setPendingWorkspaceData] = useState(null as PendingWorkspaceData | null);
  const [headerSaveState, setHeaderSaveState] = useState('idle' as 'idle' | 'saving' | 'success');
  const headerSaveTimeoutRef = useRef(null as NodeJS.Timeout | null);
  const [currentWorkspace, setCurrentWorkspace] = useState(null as string | null);
  
  // Build folder index for efficient folder selection
  const folderIndex = useMemo(() => {
    return buildFolderIndex(allFiles);
  }, [allFiles]);

  // Integration with specialized hooks
  const fileSelection = useFileSelectionState(allFiles, selectedFolder, folderIndex);
  const promptState = usePromptState();
  const modalState = useModalState();
  const docState = useDocState();
  const { saveWorkspace: persistWorkspace, loadWorkspace: loadPersistedWorkspace, getWorkspaceNames } = useWorkspaceState();
  
  // Extract specific functions from fileSelection to avoid dependency on the whole object
  const clearSelectedFiles = fileSelection.clearSelectedFiles;
  const setSelectionState = fileSelection.setSelectionState;
  const cleanupStaleSelections = fileSelection.cleanupStaleSelections;
  const selectedFiles = fileSelection.selectedFiles;
  
  // Token counter hook - always enabled
  const { countTokens: workerCountTokens, countTokensBatch, isReady: isTokenWorkerReady } = useTokenCounter();

  // Refs for state values needed in callbacks to avoid unstable dependencies
  const allFilesRef = useRef(allFiles);
  const selectedFolderRef = useRef(selectedFolder);
  const processingStatusRef = useRef(processingStatus);
  const promptStateRef = useRef(promptState); // Ref for the whole prompt state object

  // Update refs whenever state changes
  useEffect(() => { allFilesRef.current = allFiles; }, [allFiles]);
  useEffect(() => { selectedFolderRef.current = selectedFolder; }, [selectedFolder]);
  useEffect(() => { processingStatusRef.current = processingStatus; }, [processingStatus]);
  useEffect(() => { promptStateRef.current = promptState; }, [promptState]);

  // Update instructions token count when user instructions change
  const [userInstructions, setUserInstructions] = useState('');
  const [instructionsTokenCount, setInstructionsTokenCount] = useState(0);
  
  // Instructions (docs) state
  const [instructions, setInstructions] = usePersistentState<Instruction[]>(
    STORAGE_KEYS.INSTRUCTIONS,
    []
  );
  const [selectedInstructions, setSelectedInstructions] = useState(() => [] as Instruction[]);

  const handleResetFolderState = useCallback(() => {
    resetFolderState(
      setSelectedFolder,
      setAllFiles, // Use extracted variable
      fileSelection.setSelectedFiles,
      setProcessingStatus,
      setAppInitialized
    );
  }, [setSelectedFolder, setAllFiles, fileSelection.setSelectedFiles, setProcessingStatus, setAppInitialized]);

  // Ref for stable callback
  const handleResetFolderStateRef = useRef(handleResetFolderState);
  // Update ref whenever state changes
  useEffect(() => { handleResetFolderStateRef.current = handleResetFolderState; }, [handleResetFolderState]);

  // Clear header save timeout on unmount
  useEffect(() => {
    return () => {
      if (headerSaveTimeoutRef.current) {
        clearTimeout(headerSaveTimeoutRef.current);
      }
    };
  }, []);
  
  // Apply filters and sorting to files
  const handleFiltersAndSort = useCallback(
    (files: FileData[], sort: string, filter: string) => {
      return applyFiltersAndSort(files, sort, filter, setDisplayedFiles);
    },
    [setDisplayedFiles]
  );

  useEffect(() => {
    setInstructionsTokenCount(estimateTokenCount(userInstructions));
  }, [userInstructions]);

  useEffect(() => {
    const handleWorkspacesChanged = (event: CustomEvent) => {

      if (event.detail?.deleted === currentWorkspace && event.detail?.wasCurrent) {
         setCurrentWorkspace(null);
         handleResetFolderState();
      }
    };

    window.addEventListener('workspacesChanged', handleWorkspacesChanged as EventListener);

    return () => {
      window.removeEventListener('workspacesChanged', handleWorkspacesChanged as EventListener);
    };
  }, [currentWorkspace, setCurrentWorkspace, handleResetFolderState]);

  // Handle sort change
  const handleSortChange = useCallback((newSort: string) => {
    setSortOrder(newSort);
    handleFiltersAndSort(allFiles, newSort, searchTerm);
    setSortDropdownOpen(false); // Close dropdown after selection
  }, [allFiles, searchTerm, setSortOrder, handleFiltersAndSort]);

  // Add the new handler for file tree sort changes
  const handleFileTreeSortChange = useCallback((fileTreeSort: string) => {
    const mappedSort = mapFileTreeSortToContentSort(fileTreeSort);
    setSortOrder(mappedSort);
    handleFiltersAndSort(allFiles, mappedSort, searchTerm);
  }, [allFiles, searchTerm, setSortOrder, handleFiltersAndSort]);

  // Handle search change
  const handleSearchChange = useCallback((newSearch: string) => {
    setSearchTerm(newSearch);
    handleFiltersAndSort(allFiles, sortOrder, newSearch);
  }, [allFiles, sortOrder, setSearchTerm, handleFiltersAndSort]);

  // State for sort dropdown
  const [sortDropdownOpen, setSortDropdownOpen] = useState(false);

  // Toggle sort dropdown
  const toggleSortDropdown = useCallback(() => {
    setSortDropdownOpen(!sortDropdownOpen);
  }, [sortDropdownOpen]);

  // Calculate token counts for file tree modes
  const fileTreeTokenCounts = useCallback(() => {
    return calculateFileTreeTokens(allFiles, fileSelection.selectedFiles, selectedFolder);
  }, [allFiles, fileSelection.selectedFiles, selectedFolder]);

  // Get the token count for the current file tree mode
  const getCurrentFileTreeTokens = useCallback(() => {
    return getFileTreeModeTokens(allFiles, fileSelection.selectedFiles, selectedFolder, fileTreeMode);
  }, [allFiles, fileSelection.selectedFiles, selectedFolder, fileTreeMode]);

  // Handle token calculations
  const calculateTotalTokens = useCallback(() => {
    const allFilesMap = new Map(allFiles.map(file => [file.path, file]));
    
    return fileSelection.selectedFiles.reduce((total, selectedFile) => {
      const fileData = allFilesMap.get(selectedFile.path);
      if (fileData && fileData.tokenCount) {
        // If the selection has specific line ranges, estimate token count for those
        if (selectedFile.lines && selectedFile.lines.length > 0 && fileData.content) {
          const lines = fileData.content.split('\n');
          let selectedContent = '';
          for (const range of selectedFile.lines) {
            selectedContent += lines.slice(range.start - 1, range.end).join('\n') + '\n';
          }
          // Simple estimation: ~4 characters per token
          return total + Math.ceil(selectedContent.length / 4);
        } else {
          // Full file selected
          return total + fileData.tokenCount;
        }
      }
      return total;
    }, 0);
  }, [fileSelection.selectedFiles, allFiles]);

  // Open folder dialog
  const openFolder = useCallback(() => {
    if (openFolderDialog(isElectron, setProcessingStatus)) {
      setAppInitialized(true);
    }
  }, [isElectron]);

  // Handle cancel loading
  const handleCancelLoading = useCallback(() => {
    cancelFileLoading(isElectron, setProcessingStatus);
    setIsLoadingCancellable(false);
  }, [isElectron]);

  // Refresh file tree
  const handleRefreshFileTree = useCallback(() => {
    refreshFileTree(
      isElectron,
      selectedFolder,
      exclusionPatterns,
      setProcessingStatus,
      fileSelection.clearSelectedFiles
    );
  }, [isElectron, selectedFolder, exclusionPatterns, fileSelection.clearSelectedFiles]);

  // Toggle expand/collapse state changes
  const toggleExpanded = useCallback((nodeId: string) => {
    setExpandedNodes((prev: Record<string, boolean>) => {
      // If the node is not in expandedNodes, we need to set it to false
      // because it was expanded by default and user wants to collapse it
      // If it's already in expandedNodes, just toggle it
      const newValue = prev[nodeId] === undefined ? false : !prev[nodeId];
      
      const newState = {
        ...prev,
        [nodeId]: newValue,
      };

      // The usePersistentState hook will handle persistence

      return newState;
    });
  }, []);

  // Get content with prompts and instructions
  const getFormattedContent = useCallback(() => {
    return getSelectedFilesContent(
      allFiles,
      fileSelection.selectedFiles,
      sortOrder,
      fileTreeMode,
      selectedFolder,
      promptState.selectedSystemPrompts,
      promptState.selectedRolePrompts,
      selectedInstructions,
      userInstructions
    );
  }, [
    allFiles,
    fileSelection.selectedFiles,
    sortOrder,
    fileTreeMode,
    selectedFolder,
    promptState.selectedSystemPrompts,
    promptState.selectedRolePrompts,
    selectedInstructions,
    userInstructions
  ]);

  // Get content without instructions
  const getFormattedContentWithoutInstructions = useCallback(() => {
    return getSelectedFilesContentWithoutInstructions(
      allFiles,
      fileSelection.selectedFiles,
      sortOrder,
      fileTreeMode,
      selectedFolder
    );
  }, [
    allFiles,
    fileSelection.selectedFiles,
    sortOrder,
    fileTreeMode,
    selectedFolder
  ]);
  
  // Helper function to validate file load request
  const validateFileLoadRequest = useCallback((filePath: string, files: FileData[]): { valid: boolean; file?: FileData; reason?: string } => {
    const file = files.find((f: FileData) => f.path === filePath);
    
    if (!file) {
      return { valid: false, reason: 'File not found' };
    }
    
    if (file.isContentLoaded) {
      return { valid: false, reason: 'Already loaded' };
    }
    
    if (selectedFolder && !filePath.startsWith(selectedFolder)) {
      return { valid: false, reason: 'Outside workspace' };
    }
    
    return { valid: true, file };
  }, [selectedFolder]);

  // Helper function to update file loading state
  const updateFileLoadingState = useCallback((filePath: string, isLoading: boolean) => {
    flushSync(() => {
      setAllFiles((prev: FileData[]) =>
        prev.map((f: FileData) =>
          f.path === filePath
            ? { ...f, isCountingTokens: isLoading }
            : f
        )
      );
    });
    
    // Removed automatic file selection when loading content
    // Files should only be selected via explicit user action (checkbox or Apply in modal)
  }, [setAllFiles]);

  // Helper function to process token counting
  const processFileTokens = useCallback(async (
    content: string,
    filePath: string,
    priority: number = 0
  ): Promise<{ tokenCount: number; error?: string }> => {
    try {
      if (isTokenWorkerReady) {
        const tokenCount = await workerCountTokens(content, priority);
        return { tokenCount };
      } else {
        const tokenCount = estimateTokenCount(content);
        return { tokenCount, error: 'Worker not ready, used estimation' };
      }
    } catch (error) {
      console.error(`Token counting failed for ${filePath}:`, error);
      const tokenCount = estimateTokenCount(content);
      return { tokenCount, error: 'Worker failed, used estimation' };
    }
  }, [isTokenWorkerReady, workerCountTokens]);

  // Helper function to update file with content and tokens
  const updateFileWithContent = useCallback((
    filePath: string,
    content: string,
    tokenCount: number,
    tokenCountError?: string
  ) => {
    fileContentCache.set(filePath, content, tokenCount);
    
    // Invalidate token cache for this file when content changes
    tokenCountCache.invalidateFile(filePath);
    
    // Use flushSync to force React to process this update immediately
    // This fixes the issue where token counts don't appear until a second file is selected
    flushSync(() => {
      setAllFiles((prev: FileData[]) =>
        prev.map((f: FileData) =>
          f.path === filePath
            ? { 
                ...f, 
                content, 
                tokenCount, 
                isContentLoaded: true, 
                isCountingTokens: false,
                error: undefined,
                tokenCountError 
              }
            : f
        )
      );
    });
    
    // Only update selected file if it's already in the selection
    // This prevents auto-selecting files when just viewing them
    const existingSelectedFile = fileSelection.findSelectedFile(filePath);
    if (existingSelectedFile) {
      fileSelection.updateSelectedFile(filePath, existingSelectedFile.lines);
    }
  }, [setAllFiles, fileSelection]);

  // Helper function to handle cached content
  const handleCachedContent = useCallback(async (
    filePath: string,
    cached: { content: string; tokenCount?: number }
  ): Promise<boolean> => {
    if (cached.tokenCount !== undefined) {
      updateFileWithContent(filePath, cached.content, cached.tokenCount);
      return true;
    }
    
    // Need to count tokens for cached content
    const { tokenCount, error } = await processFileTokens(cached.content, filePath, 0); // High priority for visible files
    updateFileWithContent(filePath, cached.content, tokenCount, error);
    return true;
  }, [processFileTokens, updateFileWithContent]);

  const loadFileContent = useCallback(async (filePath: string): Promise<void> => {
    try {
      // Get current files state for validation
      const currentFiles = await new Promise<FileData[]>((resolve) => {
        setAllFiles((prev: FileData[]) => {
          resolve(prev);
          return prev;
        });
      });

      // Validate the request
      const validation = validateFileLoadRequest(filePath, currentFiles);
      if (!validation.valid) {
        if (validation.reason === 'Outside workspace') {
          console.warn(`Skipping file outside current workspace: ${filePath}`);
        }
        return;
      }

      // Check if already loading to prevent duplicate requests
      const file = validation.file;
      if (file && file.isCountingTokens) {
        return;
      }

      // Mark file as loading
      updateFileLoadingState(filePath, true);

      // Check cache first
      const cached = fileContentCache.get(filePath);
      if (cached) {
        await handleCachedContent(filePath, cached);
        return;
      }

      // Load from backend
      const result = await requestFileContent(filePath);
      if (result.success && result.content !== undefined) {
        // Process tokens and update state
        const { tokenCount, error } = await processFileTokens(result.content, filePath, 0); // High priority for visible files
        updateFileWithContent(filePath, result.content, tokenCount, error);
      } else {
        // Handle error
        setAllFiles((prev: FileData[]) =>
          prev.map((f: FileData) =>
            f.path === filePath 
              ? { ...f, error: result.error, isContentLoaded: false, isCountingTokens: false } 
              : f
          )
        );
      }
    } catch (error) {
      console.error(`Error loading file content for ${filePath}:`, error);
      // Ensure loading state is cleared on any error
      setAllFiles((prev: FileData[]) =>
        prev.map((f: FileData) =>
          f.path === filePath 
            ? { ...f, error: 'Failed to load file', isContentLoaded: false, isCountingTokens: false } 
            : f
        )
      );
    }
  }, [
    validateFileLoadRequest, 
    updateFileLoadingState, 
    handleCachedContent, 
    processFileTokens, 
    updateFileWithContent,
    setAllFiles
  ]);

  // Helper function to set batch loading state
  const setBatchLoadingState = useCallback((filePaths: string[], isLoading: boolean) => {
    setAllFiles((prev: FileData[]) =>
      prev.map((f: FileData) =>
        filePaths.includes(f.path)
          ? { ...f, isCountingTokens: isLoading }
          : f
      )
    );
  }, [setAllFiles]);

  // Helper function to process batch results
  const processBatchResults = useCallback((
    results: { success: boolean; content?: string; error?: string }[],
    filePaths: string[]
  ) => {
    const successful: { path: string; content: string }[] = [];
    const failed: { path: string; error: string }[] = [];

    for (const [index, result] of results.entries()) {
      const path = filePaths[index];
      if (result.success && result.content) {
        successful.push({ path, content: result.content });
      } else {
        failed.push({ path, error: result.error || 'Failed to load content' });
      }
    }

    return { successful, failed };
  }, []);

  // Helper function to update file state with token counts
  const updateFilesWithTokenCounts = useCallback((
    filePaths: string[],
    filePathToResult: Map<string, any>,
    filePathToTokenCount: Map<string, number>
  ) => {
    setAllFiles((prev: FileData[]) =>
      prev.map((f: FileData) => {
        if (!filePaths.includes(f.path)) return f;

        const result = filePathToResult.get(f.path);
        const tokenCount = filePathToTokenCount.get(f.path);

        if (result?.success && result.content !== undefined && tokenCount !== undefined) {
          fileContentCache.set(f.path, result.content, tokenCount);
          
          // Only update selected file if it's already in the selection
          const existingSelectedFile = fileSelection.findSelectedFile(f.path);
          if (existingSelectedFile) {
            fileSelection.updateSelectedFile(f.path, existingSelectedFile.lines);
          }

          return {
            ...f,
            content: result.content,
            tokenCount,
            isContentLoaded: true,
            isCountingTokens: false,
            error: undefined,
            tokenCountError: undefined
          };
        } else if (result && !result.success) {
          return {
            ...f,
            error: result.error || 'Failed to load content',
            isContentLoaded: false,
            isCountingTokens: false,
            tokenCountError: undefined
          };
        } else {
          return {
            ...f,
            tokenCountError: 'Failed to count tokens',
            isCountingTokens: false
          };
        }
      })
    );
  }, [setAllFiles, fileSelection]);

  // Helper function for fallback token counting
  const fallbackTokenCounting = useCallback(async (
    successfulLoads: { path: string; content: string }[]
  ) => {
    for (const { path, content } of successfulLoads) {
      const tokenCount = estimateTokenCount(content);
      fileContentCache.set(path, content, tokenCount);
      
      setAllFiles((prev: FileData[]) =>
        prev.map((f: FileData) =>
          f.path === path
            ? { ...f, content, tokenCount, isContentLoaded: true, isCountingTokens: false }
            : f
        )
      );
    }
  }, [setAllFiles]);

  // Batch load multiple file contents
  const loadMultipleFileContents = useCallback(async (filePaths: string[], options?: { priority?: number }): Promise<void> => {
    if (!isTokenWorkerReady) {
      for (const path of filePaths) {
        await loadFileContent(path);
      }
      return;
    }

    setBatchLoadingState(filePaths, true);

    const results = await Promise.all(
      filePaths.map(path => requestFileContent(path))
    );

    const { successful, failed } = processBatchResults(results, filePaths);

    if (successful.length > 0) {
      try {
        const contents = successful.map((item: { path: string; content: string }) => item.content);
        const tokenCounts = await countTokensBatch(contents, { priority: options?.priority ?? 10 });

        const filePathToTokenCount = new Map(
          successful.map((item: { path: string; content: string }, index: number) => [item.path, tokenCounts[index]])
        );

        const filePathToResult = new Map(
          results.map((result, index) => [filePaths[index], result])
        );

        updateFilesWithTokenCounts(filePaths, filePathToResult, filePathToTokenCount);
      } catch (error) {
        console.error('Error in batch token counting:', error);
        await fallbackTokenCounting(successful);
      }
    }

    if (failed.length > 0) {
      setAllFiles((prev: FileData[]) =>
        prev.map((f: FileData) =>
          failed.some((item: { path: string; error: string }) => item.path === f.path)
            ? { ...f, error: 'Failed to load content', isContentLoaded: false, isCountingTokens: false }
            : f
        )
      );
    }
  }, [
    isTokenWorkerReady, 
    loadFileContent, 
    countTokensBatch, 
    setBatchLoadingState, 
    processBatchResults, 
    updateFilesWithTokenCounts,
    fallbackTokenCounting,
    setAllFiles
  ]);

  // expandedNodes is managed by usePersistentState hook



  // Set up viewFile event listener
  useEffect(() => {
    const handleViewFileEvent = (event: CustomEvent) => {
      if (event.detail) {
        modalState.openFileViewModal(event.detail);
      }
    };
    
    // Add event listener
    window.addEventListener('viewFile', handleViewFileEvent as EventListener);
    
    // Cleanup
    return () => {
      window.removeEventListener('viewFile', handleViewFileEvent as EventListener);
    };
  }, [modalState]);
  
  // Set up file-list-updated event listener to handle pending workspace data
  useEffect(() => {
    const handleFileListUpdated = () => {
    };
    
    // Add event listener
    window.addEventListener('file-list-updated', handleFileListUpdated);
    
    // Cleanup
    return () => {
      window.removeEventListener('file-list-updated', handleFileListUpdated);
    };
  }, [pendingWorkspaceData, currentWorkspace, allFiles, selectedFolder]);

  // Wrap saveWorkspace in useCallback to avoid recreating it on every render
  const saveWorkspace = useCallback((name: string) => {
    // Deduplicate selected files before saving
    const uniqueSelectedFiles = [...new Map(fileSelection.selectedFiles.map(file => [file.path, file])).values()];
    
    const workspace: WorkspaceState = {
      selectedFolder: selectedFolder,
      expandedNodes: expandedNodes,
      selectedFiles: uniqueSelectedFiles,
      allFiles: allFiles,
      sortOrder: sortOrder,
      searchTerm: searchTerm,
      fileTreeMode: fileTreeMode,
      exclusionPatterns: exclusionPatterns,
      userInstructions: userInstructions,
      tokenCounts: (() => {
        const acc: { [filePath: string]: number } = {};
        const allFilesMap = new Map(allFiles.map(f => [f.path, f]));
        for (const selectedFile of fileSelection.selectedFiles) {
          const fileData = allFilesMap.get(selectedFile.path);
          acc[selectedFile.path] = fileData?.tokenCount || 0;
        }
        return acc;
      })(),
      customPrompts: {
        systemPrompts: promptState.selectedSystemPrompts,
        rolePrompts: promptState.selectedRolePrompts
      },
      instructions: instructions,
      selectedInstructions: selectedInstructions
    };

    persistWorkspace(name, workspace);

  }, [
    selectedFolder,
    expandedNodes,
    fileSelection.selectedFiles,
    allFiles,
    sortOrder,
    searchTerm,
    fileTreeMode,
    exclusionPatterns,
    userInstructions,
    promptState.selectedSystemPrompts,
    promptState.selectedRolePrompts,
    instructions,
    selectedInstructions,
    persistWorkspace
  ]);

  // Helper functions for workspace data application
  const handleFolderChange = useCallback((workspaceName: string, workspaceFolder: string | null, workspaceData: WorkspaceState) => {
    
    // CRITICAL: Cancel any in-progress file loading
    if (processingStatus.status === "processing") {
      cancelFileLoading(isElectron, setProcessingStatus);
    }
    
    // Clear file content cache when switching workspaces
    fileContentCache.clear();
    
    // Clear token count cache when switching workspaces
    tokenCountCache.clear();
    
    // Clear all files to prevent accumulation from previous workspace
    setAllFiles([]);
    
    // Clear selected files when switching workspaces
    clearSelectedFiles();
    
    setCurrentWorkspace(workspaceName);
    
    if (workspaceFolder === null) {
      handleResetFolderStateRef.current();
      setPendingWorkspaceData(null);
    } else {
      const { selectedFolder: _selectedFolder, ...restOfData } = workspaceData;
      setPendingWorkspaceData(restOfData);
      
      if (window.electron?.ipcRenderer) {
        setProcessingStatus({
          status: "processing",
          message: `Loading files from workspace folder: ${workspaceFolder}`,
          processed: 0,
          directories: 0,
          total: 0
        });
        const requestId = Math.random().toString(36).slice(2, 11);
        window.electron.ipcRenderer.send("request-file-list", workspaceFolder, exclusionPatterns || [], requestId);
      }
    }
    
    setSelectedFolder(workspaceFolder);
  }, [exclusionPatterns, setProcessingStatus, setSelectedFolder, setCurrentWorkspace, setPendingWorkspaceData, processingStatus.status, isElectron, setAllFiles, clearSelectedFiles]);

  const applyExpandedNodes = useCallback((expandedNodesFromWorkspace: Record<string, boolean>) => {
    setExpandedNodes(expandedNodesFromWorkspace || {});
    // The usePersistentState hook will handle database persistence
  }, [setExpandedNodes]);

  const applySelectedFiles = useCallback((selectedFilesToApply: SelectedFileReference[], availableFiles: FileData[]): void => {
    console.log('[useAppState.applySelectedFiles] Called with:', {
      selectedFilesToApplyCount: selectedFilesToApply?.length || 0,
      availableFilesCount: availableFiles?.length || 0,
      selectedFiles: selectedFilesToApply
    });
    
    // Deduplicate input files before applying
    const uniqueFiles = [...new Map(selectedFilesToApply.map(file => [file.path, file])).values()];
    
    // Create a map of available files for efficient lookup
    const availableFilesMap = new Map(availableFiles.map(f => [f.path, f]));

    // Filter the saved selections and restore them with proper line selection data
    const filesToSelect = uniqueFiles
      .map(savedFile => {
        const availableFile = availableFilesMap.get(savedFile.path);
        if (!availableFile) return null;
        
        // Convert SelectedFileReference to the format expected by setSelectionState
        return {
          path: savedFile.path,
          lines: savedFile.lines
        } as SelectedFileReference;
      })
      .filter((file): file is SelectedFileReference => !!file);

    
    // Always call setSelectionState even with empty array to ensure proper clearing
    // Batch state updates
    unstable_batchedUpdates(() => {
      setSelectionState(filesToSelect);
    });
  }, [setSelectionState]);

  const applyPrompts = useCallback((promptsToApply: { systemPrompts?: SystemPrompt[], rolePrompts?: RolePrompt[] }) => {
    const currentPrompts = promptStateRef.current;

    // Deselect current prompts
    for (const prompt of currentPrompts.selectedSystemPrompts) currentPrompts.toggleSystemPromptSelection(prompt)
    ;
    for (const prompt of currentPrompts.selectedRolePrompts) currentPrompts.toggleRolePromptSelection(prompt)
    ;

    // Apply new prompts
    if (promptsToApply?.systemPrompts) {
      for (const savedPrompt of promptsToApply.systemPrompts) {
        const availablePrompt = currentPrompts.systemPrompts.find((p: SystemPrompt) => p.id === savedPrompt.id);
        if (availablePrompt && !currentPrompts.selectedSystemPrompts.some((p: SystemPrompt) => p.id === availablePrompt.id)) {
          currentPrompts.toggleSystemPromptSelection(availablePrompt);
        }
      }
    }
    if (promptsToApply?.rolePrompts) {
      for (const savedPrompt of promptsToApply.rolePrompts) {
        const availablePrompt = currentPrompts.rolePrompts.find((p: RolePrompt) => p.id === savedPrompt.id);
        if (availablePrompt && !currentPrompts.selectedRolePrompts.some((p: RolePrompt) => p.id === availablePrompt.id)) {
          currentPrompts.toggleRolePromptSelection(availablePrompt);
        }
      }
    }
  }, []);

  // This function handles applying workspace data, with proper file selection management
  const applyWorkspaceData = useCallback((workspaceName: string | null, workspaceData: WorkspaceState | null) => {
    if (!workspaceData || !workspaceName) {
      console.warn("[useAppState.applyWorkspaceData] Received null workspace data or name. Cannot apply.", { workspaceName, hasData: !!workspaceData });
      setPendingWorkspaceData(null);
      return;
    }

    console.log('[useAppState.applyWorkspaceData] Applying workspace data:', {
      workspaceName,
      selectedFilesCount: workspaceData.selectedFiles?.length || 0,
      systemPromptsCount: workspaceData.customPrompts?.systemPrompts?.length || 0,
      rolePromptsCount: workspaceData.customPrompts?.rolePrompts?.length || 0,
      instructionsCount: workspaceData.instructions?.length || 0,
      selectedInstructionsCount: workspaceData.selectedInstructions?.length || 0
    });

    const currentSelectedFolder = selectedFolderRef.current;
    const currentProcessingStatus = processingStatusRef.current;
    const workspaceFolder = workspaceData.selectedFolder || null;
    const folderChanged = currentSelectedFolder !== workspaceFolder;
    const isProcessing = currentProcessingStatus.status === 'processing';

    // Don't clear files here - let applySelectedFiles handle it properly

    if (folderChanged && !isProcessing) {
      // Clear all files when folder changes to prevent accumulation
      setAllFiles([]);
      handleFolderChange(workspaceName, workspaceFolder, workspaceData);
      return;
    } else if (folderChanged && isProcessing) {
      console.warn(`[useAppState.applyWorkspaceData] Folder changed but currently processing. Cannot change folder to "${workspaceFolder}". Aborting workspace load.`);
      setPendingWorkspaceData(null);
      return;
    }

    setCurrentWorkspace(workspaceName);
    setPendingWorkspaceData(null);

    
    applyExpandedNodes(workspaceData.expandedNodes);
    applySelectedFiles(workspaceData.selectedFiles, allFilesRef.current);
    setUserInstructions(workspaceData.userInstructions || '');
    applyPrompts(workspaceData.customPrompts);
    
    // Restore instructions if they exist in the workspace
    if (workspaceData.instructions) {
      setInstructions(workspaceData.instructions);
    }
    if (workspaceData.selectedInstructions) {
      setSelectedInstructions(workspaceData.selectedInstructions);
    }
  }, [
    setPendingWorkspaceData,
    setCurrentWorkspace,
    setUserInstructions,
    handleFolderChange,
    applyExpandedNodes,
    applySelectedFiles,
    applyPrompts,
    setInstructions
  ]);

  // Store refs to get latest values in handlers
  const sortOrderRef = useRef(sortOrder);
  const searchTermRef = useRef(searchTerm);
  const currentWorkspaceRef = useRef(currentWorkspace);
  // selectedFolderRef already exists above
  
  // Update refs when values change
  useEffect(() => {
    sortOrderRef.current = sortOrder;
  }, [sortOrder]);
  
  useEffect(() => {
    searchTermRef.current = searchTerm;
  }, [searchTerm]);
  
  useEffect(() => {
    currentWorkspaceRef.current = currentWorkspace;
  }, [currentWorkspace]);

  useEffect(() => {
    if (!isElectron) return;

    try {
      electronHandlerSingleton.setup(() => {
        
        // Create wrapper functions that use refs to get latest values
        const handleFiltersAndSortWrapper = (files: FileData[], _sort: string, _filter: string) => {
          handleFiltersAndSort(files, sortOrderRef.current, searchTermRef.current);
        };
        
        const cleanup = setupElectronHandlers(
          isElectron,
          setSelectedFolder,
          setAllFiles,
          setProcessingStatus,
          fileSelection.clearSelectedFiles,
          handleFiltersAndSortWrapper,
          sortOrderRef.current,
          searchTermRef.current,
          setIsLoadingCancellable,
          setAppInitialized,
          currentWorkspaceRef.current,
          setCurrentWorkspace,
          persistWorkspace,
          getWorkspaceNames,
          selectedFolderRef.current
        );
        
        // Dispatch a custom event when handlers are set up
        const event = new CustomEvent('electron-handlers-ready');
        window.dispatchEvent(event);
        
        return cleanup;
      });
    } catch (error) {
      console.error("Error setting up Electron handlers:", error);
      setProcessingStatus({
        status: "error",
        message: `Error initializing app: ${error instanceof Error ? error.message : "Unknown error"}`,
        processed: 0,
        directories: 0,
        total: 0
      });
    }
    
    // Note: We intentionally don't cleanup on unmount because React StrictMode
    // would cause handlers to be registered/unregistered repeatedly.
    // The handlers will persist for the lifetime of the application.
    return () => {
      // Empty cleanup - handlers persist
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
      isElectron
      // Removed dependencies to ensure this only runs once
    ]);

  const saveCurrentWorkspace = useCallback(() => {
    if (!currentWorkspace) {
      console.warn("[useAppState.saveCurrentWorkspace] No current workspace selected, cannot save.");
      return;
    }
    // Clear any existing timeout
    if (headerSaveTimeoutRef.current) {
      clearTimeout(headerSaveTimeoutRef.current);
    }
    
    setHeaderSaveState('saving'); // Set state to saving

    try {
      saveWorkspace(currentWorkspace);
      setHeaderSaveState('success');

      // Set timeout to revert state
      headerSaveTimeoutRef.current = setTimeout(() => {
        setHeaderSaveState('idle');
      }, 1500); // Duration for the checkmark visibility

    } catch (error) {
      console.error(`[useAppState.saveCurrentWorkspace] Error saving workspace "${currentWorkspace}":`, error);
      setHeaderSaveState('idle');
      console.error(`Failed to save workspace "${currentWorkspace}".`);
    }
  }, [currentWorkspace, saveWorkspace]);

  const loadWorkspace = useCallback(async (name: string) => {
    try {
      const workspaceData = await loadPersistedWorkspace(name);
      
      if (workspaceData) {
          // Ensure we have the folder path before applying
          if (workspaceData.selectedFolder) {
          } else {
              console.warn(`[useAppState.loadWorkspace] Workspace "${name}" has no folder path`);
          }
          applyWorkspaceData(name, workspaceData);
      } else {
          console.error(`[useAppState.loadWorkspace] Failed to load workspace data for "${name}"`);
      }
    } catch (error) {
      console.error(`[useAppState.loadWorkspace] Error loading workspace "${name}":`, error);
    }
  }, [loadPersistedWorkspace, applyWorkspaceData]);

  // Clean up selected files when workspace changes
  useEffect(() => {
    if (selectedFolder) {
      cleanupStaleSelections();
    }
  }, [selectedFolder, cleanupStaleSelections]);

  // Initial workspace loading effect
  useEffect(() => {
    // Check if app is ready and files are loaded
    if (appInitialized && allFiles.length > 0) {
      // Mark that we've initiated workspace handling for this session
      sessionStorage.setItem("hasLoadedInitialWorkspace", "true");
    }
  }, [appInitialized, allFiles]);

  // Define the event handler using useCallback outside the effect
  const handleWorkspaceLoadedEvent = useCallback((event: CustomEvent) => {
    if (event.detail?.name && event.detail?.workspace) {
      // Apply the workspace data, including the name
      applyWorkspaceData(event.detail.name, event.detail.workspace); // Pass name and data
      sessionStorage.setItem("hasLoadedInitialWorkspace", "true"); // Mark that initial load happened
    } else {
      console.warn("[useAppState.workspaceLoadedListener] Received 'workspaceLoaded' event with missing/invalid detail.", event.detail);
    }
  }, [applyWorkspaceData]);

  // Handler for direct folder opening (not from workspace loading)
  const handleDirectFolderOpenedEvent = useCallback((event: CustomEvent) => {
    if (event.detail?.name && event.detail?.workspace) {
      // Apply the workspace data, including the name
      applyWorkspaceData(event.detail.name, event.detail.workspace); // Pass name and data
      sessionStorage.setItem("hasLoadedInitialWorkspace", "true"); // Mark that initial load happened
    } else {
      console.warn("[useAppState.directFolderOpenedListener] Received 'directFolderOpened' event with missing/invalid detail.", event.detail);
    }
  }, [applyWorkspaceData]);

  useEffect(() => {
    window.addEventListener('workspaceLoaded', handleWorkspaceLoadedEvent as EventListener);
    window.addEventListener('directFolderOpened', handleDirectFolderOpenedEvent as EventListener);
    return () => {
      window.removeEventListener('workspaceLoaded', handleWorkspaceLoadedEvent as EventListener);
      window.removeEventListener('directFolderOpened', handleDirectFolderOpenedEvent as EventListener);
    };
  }, [handleWorkspaceLoadedEvent, handleDirectFolderOpenedEvent]);

  useEffect(() => {
    // Wait for file loading to complete before applying workspace data
    if (pendingWorkspaceData && currentWorkspace && allFiles.length > 0 && processingStatus.status === "complete") {
      
      const fullWorkspaceData: WorkspaceState = {
        selectedFolder: selectedFolder,
        ...pendingWorkspaceData
      };
      
      
      applyWorkspaceData(currentWorkspace, fullWorkspaceData);
    }
  }, [allFiles.length, pendingWorkspaceData, currentWorkspace, selectedFolder, processingStatus.status, applyWorkspaceData]);

  useEffect(() => {
    const handleCreateNewWorkspaceEvent = () => {
      setCurrentWorkspace(null);
      handleResetFolderStateRef.current();
    };

    window.addEventListener('createNewWorkspace', handleCreateNewWorkspaceEvent as EventListener);
    
    return () => {
      window.removeEventListener('createNewWorkspace', handleCreateNewWorkspaceEvent as EventListener);
    };
  }, []);

  // Calculate total tokens for selected files
  const totalTokensForSelectedFiles = useMemo(() => {
    return selectedFiles.reduce((acc: number, file: SelectedFileWithLines) => {
      return acc + (file.tokenCount || estimateTokenCount(file.content || ''));
    }, 0);
  }, [selectedFiles]);

  // Calculate total tokens for system prompts
  const totalTokensForSystemPrompt = useMemo(() => {
    if (!promptState.selectedSystemPrompts) return 0;
    return promptState.selectedSystemPrompts.reduce((prev: number, f: SystemPrompt) => {
      return prev + (f.tokenCount || estimateTokenCount(f.content));
    }, 0);
  }, [promptState.selectedSystemPrompts]);

  // Calculate total tokens for role prompts
  const totalTokensForRolePrompt = useMemo(() => {
    if (!promptState.selectedRolePrompts) return 0;
    return promptState.selectedRolePrompts.reduce((prev: number, f: RolePrompt) => {
      return prev + (f.tokenCount || estimateTokenCount(f.content));
    }, 0);
  }, [promptState.selectedRolePrompts]);

  const totalTokens = useMemo(() => {
    return totalTokensForSelectedFiles + totalTokensForSystemPrompt + totalTokensForRolePrompt;
  }, [totalTokensForSelectedFiles, totalTokensForSystemPrompt, totalTokensForRolePrompt]);

  // Helper functions to reduce complexity
  const handleFileSelection = useCallback((file: FileData) => {
    if (!file.isDirectory) {
      fileSelection.toggleFileSelection(file.path);
    }
  }, [fileSelection]);

  const handleDirectoryExpansion = useCallback((file: FileData) => {
    if (file.isDirectory) {
      toggleExpanded(file.path);
    }
  }, [toggleExpanded]);

  const handleFileProcessing = useCallback(async (file: FileData) => {
    if (!file.isDirectory && !file.isContentLoaded) {
      await requestFileContent(file.path);
    }
  }, []);

  const handleFileOperations = useCallback(async (file: FileData) => {
    handleFileSelection(file);
    handleDirectoryExpansion(file);
    await handleFileProcessing(file);
  }, [handleFileSelection, handleDirectoryExpansion, handleFileProcessing]);

  const handleWorkspaceUpdate = useCallback(() => {
    return {
      selectedFolder: selectedFolderRef.current,
      allFiles: allFilesRef.current,
      selectedFiles: fileSelection.selectedFiles,
      expandedNodes,
      sortOrder,
      searchTerm,
      fileTreeMode,
      exclusionPatterns
    };
  }, [expandedNodes, sortOrder, searchTerm, fileTreeMode, exclusionPatterns, fileSelection.selectedFiles]);

  const onAddInstruction = useCallback((instruction: Instruction) => {
    setInstructions((prev: Instruction[]) => [...prev, instruction]);
  }, []);

  const onDeleteInstruction = useCallback((id: string) => {
    setInstructions((prev: Instruction[]) => prev.filter(instruction => instruction.id !== id));
    setSelectedInstructions((prev: Instruction[]) => prev.filter(instruction => instruction.id !== id));
  }, []);

  const onUpdateInstruction = useCallback((instruction: Instruction) => {
    setInstructions((prev: Instruction[]) => prev.map(i => i.id === instruction.id ? instruction : i));
    setSelectedInstructions((prev: Instruction[]) => prev.map(i => i.id === instruction.id ? instruction : i));
  }, []);

  const toggleInstructionSelection = useCallback((instruction: Instruction) => {
    setSelectedInstructions((prev: Instruction[]) => {
      const isSelected = prev.some(i => i.id === instruction.id);
      return isSelected ? prev.filter(i => i.id !== instruction.id) : [...prev, instruction];
    });
  }, []);

  return {
    isElectron,
    selectedFolder,
    allFiles,
    displayedFiles,
    sortOrder,
    searchTerm,
    fileTreeMode,
    setFileTreeMode,
    expandedNodes,
    processingStatus,
    appInitialized,
    exclusionPatterns,
    setExclusionPatterns,
    isLoadingCancellable,
    currentWorkspace,
    
    // UI state
    sortDropdownOpen,
    userInstructions,
    instructionsTokenCount,
    
    // File selection state
    ...fileSelection,
    
    // Prompts state
    ...promptState,
    
    // Modal state
    ...modalState,
    
    // Doc state
    ...docState,
    
    // Actions
    openFolder,
    handleCancelLoading,
    handleSortChange,
    handleFileTreeSortChange,
    handleSearchChange,
    toggleSortDropdown,
    setUserInstructions,
    toggleExpanded,
    handleRefreshFileTree,
    handleResetFolderState,
    handleFileOperations,
    handleWorkspaceUpdate,
    
    // Calculations
    calculateTotalTokens,
    fileTreeTokenCounts,
    getCurrentFileTreeTokens,
    systemPromptsTokens: totalTokensForSystemPrompt,
    rolePromptsTokens: totalTokensForRolePrompt,
    
    // Content formatting
    getFormattedContent,
    getFormattedContentWithoutInstructions,
    
    // Workspace management
    saveWorkspace,
    loadWorkspace,
    saveCurrentWorkspace,
    headerSaveState,

    // Lazy loading
    loadFileContent,
    loadMultipleFileContents,

    // New additions
    totalTokens,
    totalTokensForSelectedFiles,
    totalTokensForSystemPrompt,
    totalTokensForRolePrompt,

    instructions,
    selectedInstructions,
    onAddInstruction,
    onDeleteInstruction,
    onUpdateInstruction,
    toggleInstructionSelection,
  };
};

export type AppState = ReturnType<typeof useAppState>;

export default useAppState;